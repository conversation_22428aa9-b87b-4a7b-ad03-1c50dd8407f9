import React from "react";
import Image from "next/image";

const AccessibleAnywhereSection: React.FC = () => {
  return (
    <section className="py-8 md:py-12 lg:py-16 px-4 bg-gray-50">
      <div className="max-w-[1500px] mx-auto">
        {/* Header */}
        <div className="text-center mb-8 md:mb-12 lg:mb-16">
          <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-[64px] font-bold text-[#021018]">
            Accessible Anywhere
          </h2>
          <p className="text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-[36px] text-[#021018] mt-2 md:mt-4">
            Access your mobile money anywhere.
          </p>
        </div>

        {/* Feature Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6 lg:gap-8">
          {/* Mobile App Card */}
          <div className="w-full max-w-[490px] mx-auto aspect-square bg-primary rounded-2xl p-4 md:p-6 lg:p-8 text-white relative overflow-hidden">
            <div className="relative z-10">
              <h3 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-[48px] font-bold mb-0">Mobile App</h3>
              <p className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-[42px] mb-4 md:mb-6 lg:mb-8">IOS & Android</p>
            </div>
            <div className="flex justify-center absolute bottom-0 left-0">
              <img
                src="/ussd-card-mockup.png"
                alt="Mobile app interface"
                className="w-full h-full"
              />
            </div>
          </div>

          {/* USSD Card */}
          <div className="w-full max-w-[490px] mx-auto aspect-square bg-primary rounded-2xl p-4 md:p-6 lg:p-8 text-white relative overflow-hidden">
            <div className="relative z-10">
              <h3 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-[48px] font-bold mb-0">USSD</h3>
              <p className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-[42px] font-thin mt-0 leading-[1.1] mb-4 md:mb-6 lg:mb-8">
                Works on any phone, <br /> no internet required
              </p>
            </div>
            <div className="flex justify-center absolute bottom-0 left-0">
              <img
                src="/keypad-phone-mockup.png"
                alt="Mobile app interface"
                className="w-full h-full"
              />
            </div>
          </div>

          {/* Web Dashboard Card */}
          <div className="w-full max-w-[490px] mx-auto aspect-square bg-primary rounded-2xl p-4 md:p-6 lg:p-8 text-white relative overflow-hidden">
            <div className="relative z-10">
              <h3 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-[48px] font-bold mb-0">Web Dashboard</h3>
              <p className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-[42px] font-thin mt-0 leading-[1.1] mb-4 md:mb-6 lg:mb-8">
                Track and manage
                <br /> your account online
              </p>
            </div>
            <div className="flex justify-center absolute bottom-0 left-0">
              <img
                src="/dashboard-mockup.png"
                alt="Mobile app interface"
                className="w-full h-full"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AccessibleAnywhereSection;
